# Telegram 抽奖机器人

这是一个功能强大的 Telegram 机器人，专门用于群组抽奖活动管理。

## 🆕 最新更新 (v1.3.0 - 2025-08-05)

**代码库清理完成**
- 🧹 删除了冗余的缓存文件和未使用的代码
- 🔧 修复了重复的方法定义问题
- 📝 更新了版本信息和项目描述
- 🗂️ 整理了项目结构，提高代码可维护性
- 📚 完善了文档和注释
- 🔗 修复了webhook路径拼写错误

## 功能特性

### 🎉 抽奖功能
- **创建抽奖**: 支持设置多种奖品、开奖时间、参与口令等
- **多群组支持**: 一个抽奖可以在多个群组同时进行
- **自动开奖**: 到达指定时间自动开奖并公布结果
- **参与管理**: 防重复参与，支持参与者查询
- **结果通知**: 自动向所有相关群组发送开奖结果
- **随机分配**: 中奖者随机获得不同奖品
- **取消功能**: 创建者可以取消正在进行的抽奖

### 🎁 奖品管理
- **多奖品支持**: 可以设置多个不同的奖品
- **灵活输入**: 支持分号分隔和换行分隔格式
- **随机分配**: 每个中奖者获得随机奖品
- **智能显示**: 根据奖品数量自动调整显示格式

### 🔧 管理功能
- **权限控制**: 只有抽奖创建者可以管理自己的抽奖
- **实时管理**: 支持查看、取消正在进行的抽奖
- **多群组管理**: 统一管理多个群组的抽奖活动

## 使用方法

### 创建抽奖
1. 私聊机器人发送 `/create` 命令
2. 按步骤设置：
   - 活动名称
   - 封面图片（可选）
   - 奖品信息（支持多个奖品）
   - 参与口令
   - 开奖时间
   - 目标群组

### 参与抽奖
- 在群组中发送抽奖口令即可参与

### 管理抽奖
- `/my_lotteries` - 查看我的抽奖活动
- `/stop_lottery` - 取消正在进行的抽奖

## 奖品设置格式

### 单个奖品
```
iPhone 15,1
```

### 多个奖品（分号分隔）
```
iPhone 15,1;现金红包,5;京东卡,3
```

### 多个奖品（换行分隔）
```
iPhone 15,1
现金红包,5
京东卡,3
```

## 群组设置格式

### 支持的格式
- 群组用户名：`@groupname`
- 群组ID：`-1001234567890`
- 群组链接：`https://t.me/groupname`
- 多个群组：`@group1,-1001234567890,https://t.me/group2`

## 安装和配置

### 环境要求
- Python 3.8+
- pip

### 安装步骤
1. 克隆项目
```bash
git clone <repository-url>
cd TgAdBot
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置机器人
```bash
cp config.example.py config.py
# 编辑 config.py 文件，填入你的机器人 Token
```

4. 运行机器人
```bash
python main.py
```

## 配置说明

在 `config.py` 中配置以下参数：

```python
class Settings:
    # 机器人基本配置
    BOT_TOKEN: str = "YOUR_BOT_TOKEN"
    
    # 新成员验证配置
    MEMBER_VERIFICATION_ENABLED: bool = False
    VERIFICATION_TIMEOUT: int = 300  # 验证超时时间（秒）
```

## 项目结构

```
TgAdBot/
├── main.py                 # 主程序入口
├── config.py              # 配置文件
├── requirements.txt       # 依赖包列表
├── src/                   # 源代码目录
│   ├── core/             # 核心功能模块
│   │   ├── decorators.py # 装饰器
│   │   ├── logger.py     # 日志系统
│   │   ├── lottery_manager.py # 抽奖管理器
│   │   └── scheduler.py  # 任务调度器
│   ├── handlers/         # 消息处理器
│   │   ├── lottery_handler.py # 抽奖处理器
│   │   └── member_verification.py # 成员验证
│   ├── models/           # 数据模型
│   │   └── lottery.py    # 抽奖模型
│   └── config/           # 配置模块
│       └── lottery_config.py # 抽奖配置
└── data/                 # 数据存储目录
    └── lotteries.json    # 抽奖数据
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 支持

如果你觉得这个项目有用，请给它一个 ⭐️！
