"""
日志系统模块

提供统一的日志配置和管理
"""

import logging
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path


def setup_logger(name: str = "telegram_bot", log_dir: str = "logs") -> logging.Logger:
    """
    设置并配置日志记录器

    Args:
        name: 日志记录器名称
        log_dir: 日志文件存储目录

    Returns:
        配置好的日志记录器实例
    """
    # 创建日志目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # 防止日志重复记录
    if not logger.handlers:
        # 创建文件处理器 - 每天轮换一次日志文件
        file_handler = TimedRotatingFileHandler(
            filename=f"{log_dir}/app.log",
            when="midnight",
            interval=1,
            backupCount=7  # 保留最近7天的日志
        )
        file_handler.setLevel(logging.INFO)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)

        # 创建格式化器并添加到处理器
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 将处理器添加到日志记录器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

    return logger


# 创建默认日志记录器实例
default_logger = setup_logger()
