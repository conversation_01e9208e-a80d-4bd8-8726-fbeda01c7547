"""
处理器加载器模块

自动发现和加载所有处理器模块
"""

import importlib
import pkgutil
from typing import List
from telegram.ext import Application
from .registry import get_registry
from .logger import default_logger as logger


def load_all_handlers():
    """
    自动加载所有处理器模块
    
    这个函数会扫描 src.handlers 包下的所有模块，
    并导入它们以触发装饰器注册处理器
    """
    try:
        logger.info("开始加载处理器模块...")
        
        # 导入处理器包
        import src.handlers
        
        # 获取包路径
        package_path = src.handlers.__path__
        package_name = src.handlers.__name__
        
        # 遍历包中的所有模块（包括子包）
        for importer, modname, ispkg in pkgutil.walk_packages(package_path, package_name + "."):
            try:
                logger.info(f"加载模块: {modname}")
                importlib.import_module(modname)
            except Exception as e:
                logger.error(f"加载模块 {modname} 时出错: {e}")
        
        logger.info("处理器模块加载完成")
        
    except Exception as e:
        logger.error(f"加载处理器时出错: {e}")
        raise


def register_handlers_to_application(app: Application):
    """
    将所有已注册的处理器添加到 Telegram Application
    
    Args:
        app: Telegram Application 实例
    """
    try:
        registry = get_registry()
        handlers = registry.get_all_handlers()
        
        logger.info(f"开始注册 {len(handlers)} 个处理器到 Application...")
        
        for handler in handlers:
            app.add_handler(handler)
            logger.debug(f"已注册处理器: {handler.__class__.__name__}")
        
        logger.info(f"成功注册 {len(handlers)} 个处理器")
        
    except Exception as e:
        logger.error(f"注册处理器到 Application 时出错: {e}")
        raise


def get_handlers_info() -> List[dict]:
    """
    获取所有已注册处理器的信息
    
    Returns:
        包含处理器信息的字典列表
    """
    try:
        registry = get_registry()
        handlers = registry.get_all_handlers()
        
        handlers_info = []
        
        for handler in handlers:
            info = {
                "type": handler.__class__.__name__,
                "callback": handler.callback.__name__ if hasattr(handler, 'callback') else "unknown"
            }
            
            # 添加特定类型的信息
            if hasattr(handler, 'command'):
                info["command"] = handler.command
            elif hasattr(handler, 'filters'):
                info["filters"] = str(handler.filters)
            elif hasattr(handler, 'pattern'):
                info["pattern"] = handler.pattern
            
            handlers_info.append(info)
        
        return handlers_info
        
    except Exception as e:
        logger.error(f"获取处理器信息时出错: {e}")
        return []


def clear_registry():
    """清空处理器注册表"""
    try:
        registry = get_registry()
        registry.clear()
        logger.info("处理器注册表已清空")
    except Exception as e:
        logger.error(f"清空注册表时出错: {e}")
