"""
老师标签管理器

管理老师数据的增删改查和标签搜索功能
"""

import json
import os
from typing import List, Dict, Optional, Any
from pathlib import Path
from src.models.teacher import Teacher
from .logger import default_logger as logger


class TeacherManager:
    """老师标签管理器"""
    
    def __init__(self, data_file: str = "data/teachers.json"):
        """
        初始化管理器
        
        Args:
            data_file: 数据文件路径
        """
        self.data_file = data_file
        self.teachers: Dict[str, Teacher] = {}
        self._ensure_data_file()
        self.load_teachers()
    
    def _ensure_data_file(self):
        """确保数据文件存在"""
        data_path = Path(self.data_file)
        data_path.parent.mkdir(parents=True, exist_ok=True)
        
        if not data_path.exists():
            # 创建空的数据文件
            with open(data_path, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            logger.info(f"创建老师数据文件: {self.data_file}")
    
    def load_teachers(self):
        """从文件加载老师数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.teachers = {}
            for teacher_id, teacher_data in data.items():
                self.teachers[teacher_id] = Teacher.from_dict(teacher_data)
            
            logger.info(f"加载了 {len(self.teachers)} 个老师数据")
            
        except Exception as e:
            logger.error(f"加载老师数据时出错: {e}")
            self.teachers = {}
    
    def save_teachers(self):
        """保存老师数据到文件"""
        try:
            data = {}
            for teacher_id, teacher in self.teachers.items():
                data[teacher_id] = teacher.to_dict()
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存了 {len(self.teachers)} 个老师数据")
            
        except Exception as e:
            logger.error(f"保存老师数据时出错: {e}")
            raise
    
    def add_teacher(self, teacher: Teacher) -> str:
        """
        添加老师
        
        Args:
            teacher: 老师对象
            
        Returns:
            老师ID
        """
        # 生成唯一ID（使用昵称+时间戳）
        import time
        teacher_id = f"{teacher.nickname}_{int(time.time())}"
        
        # 确保标签都是小写
        teacher.tags = [tag.strip().lower() for tag in teacher.tags if tag.strip()]
        
        self.teachers[teacher_id] = teacher
        self.save_teachers()
        
        logger.info(f"添加老师: {teacher.nickname} (ID: {teacher_id})")
        return teacher_id
    
    def remove_teacher(self, teacher_id: str) -> bool:
        """
        删除老师
        
        Args:
            teacher_id: 老师ID
            
        Returns:
            是否删除成功
        """
        if teacher_id in self.teachers:
            teacher = self.teachers.pop(teacher_id)
            self.save_teachers()
            logger.info(f"删除老师: {teacher.nickname} (ID: {teacher_id})")
            return True
        return False
    
    def get_teacher(self, teacher_id: str) -> Optional[Teacher]:
        """
        获取老师信息
        
        Args:
            teacher_id: 老师ID
            
        Returns:
            老师对象或None
        """
        return self.teachers.get(teacher_id)
    
    def get_all_teachers(self) -> List[Teacher]:
        """获取所有老师"""
        return list(self.teachers.values())
    
    def search_by_tags(self, tags: List[str], match_all: bool = False) -> List[Teacher]:
        """
        根据标签搜索老师
        
        Args:
            tags: 搜索的标签列表
            match_all: 是否需要匹配所有标签（True）还是匹配任意标签（False）
            
        Returns:
            匹配的老师列表
        """
        if not tags:
            return []
        
        # 清理和标准化标签
        search_tags = [tag.strip().lower() for tag in tags if tag.strip()]
        if not search_tags:
            return []
        
        matched_teachers = []
        
        for teacher in self.teachers.values():
            if match_all:
                # 需要匹配所有标签
                if teacher.has_all_tags(search_tags):
                    matched_teachers.append(teacher)
            else:
                # 匹配任意标签即可
                if teacher.has_any_tags(search_tags):
                    matched_teachers.append(teacher)
        
        logger.info(f"标签搜索 {search_tags} (match_all={match_all}): 找到 {len(matched_teachers)} 个老师")
        return matched_teachers
    
    def search_by_nickname(self, nickname: str) -> List[Teacher]:
        """
        根据昵称搜索老师
        
        Args:
            nickname: 昵称关键词
            
        Returns:
            匹配的老师列表
        """
        nickname = nickname.strip().lower()
        matched_teachers = []
        
        for teacher in self.teachers.values():
            if nickname in teacher.nickname.lower():
                matched_teachers.append(teacher)
        
        return matched_teachers
    
    def get_all_tags(self) -> List[str]:
        """获取所有标签"""
        all_tags = set()
        for teacher in self.teachers.values():
            all_tags.update(teacher.tags)
        return sorted(list(all_tags))
    
    def get_teachers_count(self) -> int:
        """获取老师总数"""
        return len(self.teachers)


# 全局老师管理器实例
teacher_manager = TeacherManager()
