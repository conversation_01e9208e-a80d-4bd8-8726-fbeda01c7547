"""
装饰器模块

提供用于自动注册 Telegram Bot 处理器的装饰器
"""

from typing import Callable
from telegram.ext import Command<PERSON><PERSON><PERSON>, MessageHandler, CallbackQueryHandler, filters
from .registry import registry
from .logger import default_logger as logger


def command_handler(command: str, description: str = "", priority: int = 0):
    """
    命令处理器装饰器

    Args:
        command: 命令名称（不包含 /）
        description: 命令描述
        priority: 优先级，数值越大优先级越高
    """
    def decorator(func: Callable):
        handler = CommandHandler(command, func)
        registry.add_handler(handler, priority)

        # 添加元数据
        func._command_name = command
        func._description = description
        func._priority = priority

        logger.info(f"注册命令处理器: /{command} - {description} (优先级: {priority})")
        return func

    return decorator


def message_handler(filter_type=filters.TEXT & ~filters.COMMAND, description: str = "", priority: int = 0):
    """
    消息处理器装饰器

    Args:
        filter_type: 消息过滤器
        description: 处理器描述
        priority: 优先级，数值越大优先级越高
    """
    def decorator(func: Callable):
        handler = MessageHandler(filter_type, func)
        registry.add_handler(handler, priority)

        # 添加元数据
        func._filter_type = filter_type
        func._description = description
        func._priority = priority

        logger.info(f"注册消息处理器: {description} (优先级: {priority})")
        return func

    return decorator


def callback_query_handler(pattern: str = None, description: str = "", priority: int = 0):
    """
    回调查询处理器装饰器

    Args:
        pattern: 回调数据匹配模式
        description: 处理器描述
        priority: 优先级，数值越大优先级越高
    """
    def decorator(func: Callable):
        handler = CallbackQueryHandler(func, pattern=pattern)
        registry.add_handler(handler, priority)

        # 添加元数据
        func._pattern = pattern
        func._description = description
        func._priority = priority

        logger.info(f"注册回调查询处理器: {description} (优先级: {priority})")
        return func

    return decorator
