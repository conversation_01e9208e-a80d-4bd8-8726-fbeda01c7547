"""
定时任务管理模块

处理抽奖自动开奖等定时任务
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Callable, Any
from src.core.logger import default_logger as logger


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.tasks: Dict[str, asyncio.Task] = {}
        self.running = False
    
    async def start(self):
        """启动调度器"""
        if self.running:
            return
        
        self.running = True
        logger.info("任务调度器已启动")
        
        # 启动主循环
        asyncio.create_task(self._main_loop())
    
    async def stop(self):
        """停止调度器"""
        self.running = False
        
        # 取消所有任务
        for task_id, task in self.tasks.items():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self.tasks.clear()
        logger.info("任务调度器已停止")
    
    async def _main_loop(self):
        """主循环，定期检查和清理任务"""
        while self.running:
            try:
                # 清理已完成的任务
                completed_tasks = [
                    task_id for task_id, task in self.tasks.items() 
                    if task.done()
                ]
                
                for task_id in completed_tasks:
                    del self.tasks[task_id]
                
                # 等待一段时间再检查
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"任务调度器主循环出错: {e}")
                await asyncio.sleep(60)
    
    def schedule_task(self, task_id: str, delay_seconds: float, 
                     callback: Callable, *args, **kwargs):
        """调度一个延时任务"""
        if task_id in self.tasks:
            # 如果任务已存在，先取消
            self.cancel_task(task_id)
        
        async def task_wrapper():
            try:
                await asyncio.sleep(delay_seconds)
                if asyncio.iscoroutinefunction(callback):
                    await callback(*args, **kwargs)
                else:
                    callback(*args, **kwargs)
            except asyncio.CancelledError:
                logger.info(f"任务 {task_id} 被取消")
            except Exception as e:
                logger.error(f"执行任务 {task_id} 时出错: {e}")
        
        task = asyncio.create_task(task_wrapper())
        self.tasks[task_id] = task
        
        logger.info(f"调度任务 {task_id}，将在 {delay_seconds} 秒后执行")
        return task
    
    def schedule_at(self, task_id: str, target_time: datetime, 
                   callback: Callable, *args, **kwargs):
        """调度一个定时任务"""
        now = datetime.now()
        if target_time <= now:
            logger.warning(f"任务 {task_id} 的目标时间已过期")
            return None
        
        delay_seconds = (target_time - now).total_seconds()
        return self.schedule_task(task_id, delay_seconds, callback, *args, **kwargs)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if not task.done():
                task.cancel()
            del self.tasks[task_id]
            logger.info(f"任务 {task_id} 已取消")
            return True
        return False
    
    def get_task_status(self, task_id: str) -> str:
        """获取任务状态"""
        if task_id not in self.tasks:
            return "not_found"
        
        task = self.tasks[task_id]
        if task.done():
            if task.cancelled():
                return "cancelled"
            elif task.exception():
                return "failed"
            else:
                return "completed"
        else:
            return "running"
    
    def list_tasks(self) -> Dict[str, str]:
        """列出所有任务及其状态"""
        return {task_id: self.get_task_status(task_id) 
                for task_id in self.tasks.keys()}


# 全局调度器实例
scheduler = TaskScheduler()
