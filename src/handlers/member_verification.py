"""
新成员验证处理器

当新成员加入群组时，自动限制其权限并发送数学验证题
用户必须在5分钟内正确回答才能解除限制
"""

import random
from datetime import datetime, timedelta
from typing import Dict
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ChatPermissions
from telegram.ext import ContextTypes
from telegram.error import BadRequest, Forbidden
from src.core.decorators import message_handler, callback_query_handler
from src.core.logger import default_logger as logger
from telegram.ext import filters
from config import settings


# 存储待验证用户的信息
pending_verifications: Dict[str, Dict] = {}  # key: f"{chat_id}_{user_id}", value: verification_info
verification_messages: Dict[str, int] = {}  # key: f"{chat_id}_{user_id}", value: message_id


async def delete_message(message, context: ContextTypes.DEFAULT_TYPE) -> bool:
    """删除消息的辅助函数"""
    try:
        await context.bot.delete_message(
            chat_id=message.chat.id,
            message_id=message.message_id
        )
        return True
    except Exception as e:
        logger.warning(f"删除消息失败: {e}")
        return False


def generate_math_question():
    """生成随机数学题"""
    operators = ['+', '-', '*']
    operator = random.choice(operators)
    
    if operator == '+':
        # 加法：1-50 + 1-50
        a = random.randint(1, 50)
        b = random.randint(1, 50)
        correct_answer = a + b
        question = f"{a} + {b}"
    elif operator == '-':
        # 减法：确保结果为正数
        a = random.randint(10, 100)
        b = random.randint(1, a-1)
        correct_answer = a - b
        question = f"{a} - {b}"
    else:  # operator == '*'
        # 乘法：1-12 * 1-12 (九九乘法表范围)
        a = random.randint(1, 12)
        b = random.randint(1, 12)
        correct_answer = a * b
        question = f"{a} × {b}"
    
    # 生成错误答案（确保与正确答案不同）
    wrong_answers = set()
    while len(wrong_answers) < 1:
        if operator == '+':
            wrong = correct_answer + random.randint(-10, 10)
        elif operator == '-':
            wrong = correct_answer + random.randint(-5, 15)
        else:  # multiplication
            wrong = correct_answer + random.randint(-20, 20)
        
        if wrong != correct_answer and wrong > 0:
            wrong_answers.add(wrong)
    
    wrong_answer = list(wrong_answers)[0]
    
    return question, correct_answer, wrong_answer


async def restrict_user(chat_id: int, user_id: int, context: ContextTypes.DEFAULT_TYPE) -> bool:
    """限制用户权限"""
    try:
        # 设置限制权限：不能发送消息、媒体等
        permissions = ChatPermissions(
            can_send_messages=False,
            can_send_audios=False,
            can_send_documents=False,
            can_send_photos=False,
            can_send_videos=False,
            can_send_video_notes=False,
            can_send_voice_notes=False,
            can_send_polls=False,
            can_send_other_messages=False,
            can_add_web_page_previews=False,
            can_change_info=False,
            can_invite_users=False,
            can_pin_messages=False
        )
        
        await context.bot.restrict_chat_member(
            chat_id=chat_id,
            user_id=user_id,
            permissions=permissions
        )
        
        logger.info(f"已限制用户 {user_id} 在群组 {chat_id} 的权限")
        return True
        
    except (BadRequest, Forbidden) as e:
        logger.error(f"限制用户权限失败: {e}")
        return False


async def unrestrict_user(chat_id: int, user_id: int, context: ContextTypes.DEFAULT_TYPE) -> bool:
    """解除用户权限限制"""
    try:
        # 恢复正常权限
        permissions = ChatPermissions(
            can_send_messages=True,
            can_send_audios=True,
            can_send_documents=True,
            can_send_photos=True,
            can_send_videos=True,
            can_send_video_notes=True,
            can_send_voice_notes=True,
            can_send_polls=True,
            can_send_other_messages=True,
            can_add_web_page_previews=True,
            can_change_info=False,  # 普通成员不能修改群组信息
            can_invite_users=True,
            can_pin_messages=False  # 普通成员不能置顶消息
        )
        
        await context.bot.restrict_chat_member(
            chat_id=chat_id,
            user_id=user_id,
            permissions=permissions
        )
        
        logger.info(f"已解除用户 {user_id} 在群组 {chat_id} 的权限限制")
        return True
        
    except (BadRequest, Forbidden) as e:
        logger.error(f"解除用户权限限制失败: {e}")
        return False


async def cleanup_verification(chat_id: int, user_id: int, context: ContextTypes.DEFAULT_TYPE):
    """清理验证相关数据"""
    verification_key = f"{chat_id}_{user_id}"
    
    # 删除验证消息
    if verification_key in verification_messages:
        try:
            message_id = verification_messages[verification_key]
            await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        except Exception as e:
            logger.warning(f"删除验证消息失败: {e}")
        finally:
            del verification_messages[verification_key]
    
    # 清理验证数据
    if verification_key in pending_verifications:
        del pending_verifications[verification_key]


async def verification_timeout_callback(context: ContextTypes.DEFAULT_TYPE):
    """验证超时回调"""
    job_data = context.job.data
    chat_id = job_data['chat_id']
    user_id = job_data['user_id']
    
    verification_key = f"{chat_id}_{user_id}"
    
    # 检查用户是否还在待验证状态
    if verification_key in pending_verifications:
        try:
            # 清理验证数据
            await cleanup_verification(chat_id, user_id, context)
            
            # 发送超时通知
            timeout_message = f"⏰ 用户验证超时，用户 {user_id} 未能在规定时间内完成验证"
            await context.bot.send_message(
                chat_id=chat_id,
                text=timeout_message
            )
            
            logger.info(f"用户 {user_id} 在群组 {chat_id} 验证超时")
            
        except Exception as e:
            logger.error(f"处理验证超时时出错: {e}")


@message_handler(filters.StatusUpdate.NEW_CHAT_MEMBERS, "新成员验证")
async def handle_new_member_verification(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    处理新成员加入的验证流程
    启动新成员验证流程（如果启用）
    """
    message = update.message
    if not message or not message.new_chat_members:
        return

    # 只处理群组消息
    if message.chat.type not in ['group', 'supergroup']:
        return

    # 如果新成员验证功能未启用，直接返回
    if not settings.MEMBER_VERIFICATION_ENABLED:
        return
    
    chat_id = message.chat.id
    
    for new_member in message.new_chat_members:
        # 跳过机器人
        if new_member.is_bot:
            continue
        
        user_id = new_member.id
        user_name = new_member.first_name
        username = f"@{new_member.username}" if new_member.username else f"ID:{user_id}"
        
        try:
            # 1. 限制新用户权限
            if not await restrict_user(chat_id, user_id, context):
                logger.warning(f"无法限制用户 {user_id} 的权限，跳过验证")
                continue
            
            # 2. 生成数学题
            question, correct_answer, wrong_answer = generate_math_question()
            
            # 3. 随机排列答案按钮
            answers = [correct_answer, wrong_answer]
            random.shuffle(answers)
            
            # 4. 创建内联键盘
            keyboard = []
            for answer in answers:
                callback_data = f"verify_{chat_id}_{user_id}_{answer}_{correct_answer}"
                keyboard.append([InlineKeyboardButton(str(answer), callback_data=callback_data)])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            # 5. 发送验证消息
            timeout_minutes = settings.VERIFICATION_TIMEOUT_MINUTES
            verification_text = f"""🔐 **新成员验证**

👋 欢迎 {user_name} ({username}) 加入群组！

为了防止垃圾信息，请在 **{timeout_minutes}分钟** 内完成以下数学题验证：

🧮 **{question} = ?**

请点击正确答案："""
            
            verification_msg = await context.bot.send_message(
                chat_id=chat_id,
                text=verification_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
            # 6. 记录验证信息
            verification_key = f"{chat_id}_{user_id}"
            pending_verifications[verification_key] = {
                'user_id': user_id,
                'user_name': user_name,
                'username': username,
                'chat_id': chat_id,
                'question': question,
                'correct_answer': correct_answer,
                'start_time': datetime.now(),
                'message_id': verification_msg.message_id
            }
            
            verification_messages[verification_key] = verification_msg.message_id
            
            # 7. 设置超时任务
            timeout_seconds = settings.VERIFICATION_TIMEOUT_MINUTES * 60
            context.job_queue.run_once(
                verification_timeout_callback,
                when=timeout_seconds,
                data={'chat_id': chat_id, 'user_id': user_id},
                name=f"verification_timeout_{chat_id}_{user_id}"
            )
            
            logger.info(f"为新成员 {username} (ID:{user_id}) 在群组 {chat_id} 启动验证流程")
            
        except Exception as e:
            logger.error(f"处理新成员验证时出错: {e}")


@callback_query_handler("verify_", "处理验证答案")
async def handle_verification_answer(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理用户点击的验证答案"""
    query = update.callback_query
    await query.answer()
    
    try:
        # 解析回调数据: verify_{chat_id}_{user_id}_{selected_answer}_{correct_answer}
        callback_data = query.data
        parts = callback_data.split('_')
        
        if len(parts) != 5:
            await query.edit_message_text("❌ 验证数据格式错误")
            return
        
        chat_id = int(parts[1])
        target_user_id = int(parts[2])
        selected_answer = int(parts[3])
        correct_answer = int(parts[4])
        
        # 检查是否是目标用户本人点击
        clicking_user_id = query.from_user.id
        if clicking_user_id != target_user_id:
            await query.answer("❌ 只有被验证的用户才能回答", show_alert=True)
            return
        
        verification_key = f"{chat_id}_{target_user_id}"
        
        # 检查验证是否还有效
        if verification_key not in pending_verifications:
            await query.edit_message_text("❌ 验证已过期或已完成")
            return
        
        verification_info = pending_verifications[verification_key]
        user_name = verification_info['user_name']
        username = verification_info['username']
        
        # 取消超时任务
        job_name = f"verification_timeout_{chat_id}_{target_user_id}"
        current_jobs = context.job_queue.get_jobs_by_name(job_name)
        for job in current_jobs:
            job.schedule_removal()
        
        if selected_answer == correct_answer:
            # 答案正确
            success_text = f"""✅ **验证成功！**

🎉 恭喜 {user_name} ({username}) 验证通过！

现在您可以正常发言了。欢迎加入我们的群组！"""
            
            await query.edit_message_text(success_text, parse_mode='Markdown')
            
            # 解除用户权限限制
            await unrestrict_user(chat_id, target_user_id, context)
            
            logger.info(f"用户 {username} (ID:{target_user_id}) 在群组 {chat_id} 验证成功")
            
        else:
            # 答案错误
            fail_text = f"""❌ **验证失败！**

😞 {user_name} ({username}) 回答错误。

正确答案是: **{correct_answer}**

您的发言权限将继续受到限制。如需帮助，请联系群组管理员。"""
            
            await query.edit_message_text(fail_text, parse_mode='Markdown')
            
            logger.info(f"用户 {username} (ID:{target_user_id}) 在群组 {chat_id} 验证失败")
        
        # 清理验证数据
        await cleanup_verification(chat_id, target_user_id, context)
        
    except Exception as e:
        logger.error(f"处理验证答案时出错: {e}")
        await query.edit_message_text("❌ 处理验证时发生错误，请联系管理员")


# 管理员命令：手动解除用户限制
@message_handler(filters.COMMAND & filters.Regex(r'^/unlock'), "手动解锁用户")
async def manual_unlock_user(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """管理员手动解锁用户"""
    message = update.message
    if not message:
        return
    
    # 检查是否是群组
    if message.chat.type not in ['group', 'supergroup']:
        await message.reply_text("❌ 此命令只能在群组中使用")
        return
    
    # 检查管理员权限
    try:
        chat_member = await context.bot.get_chat_member(message.chat.id, message.from_user.id)
        if chat_member.status not in ['administrator', 'creator']:
            await message.reply_text("❌ 只有管理员才能使用此命令")
            return
    except Exception:
        await message.reply_text("❌ 无法验证管理员权限")
        return
    
    # 解析用户ID
    if not context.args:
        await message.reply_text("❌ 请提供用户ID\n用法: `/unlock <用户ID>`", parse_mode='Markdown')
        return
    
    try:
        user_id = int(context.args[0])
        chat_id = message.chat.id
        
        # 解除限制
        if await unrestrict_user(chat_id, user_id, context):
            # 清理验证数据
            await cleanup_verification(chat_id, user_id, context)
            
            await message.reply_text(f"✅ 已手动解除用户 {user_id} 的限制")
            logger.info(f"管理员 {message.from_user.id} 手动解除了用户 {user_id} 在群组 {chat_id} 的限制")
        else:
            await message.reply_text(f"❌ 解除用户 {user_id} 限制失败")
    
    except ValueError:
        await message.reply_text("❌ 用户ID必须是数字")
    except Exception as e:
        logger.error(f"手动解锁用户时出错: {e}")
        await message.reply_text("❌ 操作失败，请检查用户ID是否正确")


# 查看待验证用户列表
@message_handler(filters.COMMAND & filters.Regex(r'^/pending'), "查看待验证用户")
async def list_pending_verifications(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """查看当前群组的待验证用户列表"""
    message = update.message
    if not message:
        return
    
    # 检查是否是群组
    if message.chat.type not in ['group', 'supergroup']:
        await message.reply_text("❌ 此命令只能在群组中使用")
        return
    
    # 检查管理员权限
    try:
        chat_member = await context.bot.get_chat_member(message.chat.id, message.from_user.id)
        if chat_member.status not in ['administrator', 'creator']:
            await message.reply_text("❌ 只有管理员才能使用此命令")
            return
    except Exception:
        await message.reply_text("❌ 无法验证管理员权限")
        return
    
    chat_id = message.chat.id
    
    # 筛选当前群组的待验证用户
    current_chat_pending = {
        key: info for key, info in pending_verifications.items()
        if info['chat_id'] == chat_id
    }
    
    if not current_chat_pending:
        await message.reply_text("✅ 当前没有待验证的用户")
        return
    
    text = "📋 **待验证用户列表**\n\n"
    
    for i, (key, info) in enumerate(current_chat_pending.items(), 1):
        elapsed_time = datetime.now() - info['start_time']
        timeout_minutes = settings.VERIFICATION_TIMEOUT_MINUTES
        remaining_time = timedelta(minutes=timeout_minutes) - elapsed_time

        if remaining_time.total_seconds() > 0:
            remaining_minutes = int(remaining_time.total_seconds() // 60)
            remaining_seconds = int(remaining_time.total_seconds() % 60)
            time_info = f"剩余 {remaining_minutes}:{remaining_seconds:02d}"
        else:
            time_info = "已超时"
        
        text += f"{i}. {info['user_name']} ({info['username']})\n"
        text += f"   题目: {info['question']}\n"
        text += f"   状态: {time_info}\n\n"
    
    await message.reply_text(text, parse_mode='Markdown')
