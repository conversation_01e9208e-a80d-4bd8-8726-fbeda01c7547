"""
老师标签搜索处理器

处理老师标签搜索相关的命令和交互
"""

import re
from typing import List
from telegram import Update
from telegram.ext import ContextTypes, filters
from src.core.decorators import message_handler
from src.core.teacher_manager import teacher_manager
from src.core.logger import default_logger as logger


def parse_search_tags(text: str) -> List[str]:
    """
    解析搜索标签命令
    
    Args:
        text: 消息文本
        
    Returns:
        标签列表，如果不是搜索命令则返回空列表
    """
    # 匹配 search_tag 命令格式
    pattern = r'^search_tag\s+(.+)$'
    match = re.match(pattern, text.strip(), re.IGNORECASE)
    
    if not match:
        return []
    
    # 提取标签部分
    tags_text = match.group(1).strip()
    
    # 按逗号分割标签
    tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()]
    
    return tags


def format_teacher_result(teachers: List) -> str:
    """
    格式化老师搜索结果
    
    Args:
        teachers: 老师列表
        
    Returns:
        格式化的结果字符串
    """
    if not teachers:
        return "暂无匹配结果"
    
    result_lines = []
    result_lines.append(f"🔍 找到 {len(teachers)} 位老师：\n")
    
    for i, teacher in enumerate(teachers, 1):
        teacher_info = f"{i}. "
        
        # 添加昵称
        teacher_info += f"**{teacher.nickname}**"
        
        # 添加用户名
        if teacher.username:
            teacher_info += f" (@{teacher.username})"
        
        # 添加频道链接
        if teacher.channel_link:
            teacher_info += f"\n   📺 频道: {teacher.channel_link}"
        
        # 添加标签
        if teacher.tags:
            teacher_info += f"\n   🏷️ 标签: {', '.join(teacher.tags)}"
        
        result_lines.append(teacher_info)
    
    return "\n\n".join(result_lines)


@message_handler(
    filters.TEXT & ~filters.COMMAND & filters.ChatType.GROUPS,
    "处理老师标签搜索",
    priority=5  # 设置较低优先级，让抽奖口令处理器先执行
)
async def handle_teacher_search(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理群组中的老师标签搜索命令"""
    message = update.message
    if not message or not message.text:
        return
    
    text = message.text.strip()
    
    # 解析搜索标签
    search_tags = parse_search_tags(text)
    if not search_tags:
        return  # 不是搜索命令，让其他处理器继续处理
    
    logger.info(f"用户 {message.from_user.id} 在群组 {message.chat.id} 搜索标签: {search_tags}")
    
    try:
        # 搜索匹配的老师（匹配任意标签）
        matched_teachers = teacher_manager.search_by_tags(search_tags, match_all=False)
        
        # 格式化结果
        result_text = format_teacher_result(matched_teachers)
        
        # 回复搜索结果
        await message.reply_text(
            result_text,
            parse_mode='Markdown',
            disable_web_page_preview=True
        )
        
        logger.info(f"标签搜索完成，返回 {len(matched_teachers)} 个结果")
        
    except Exception as e:
        logger.error(f"处理标签搜索时出错: {e}")
        await message.reply_text("❌ 搜索时发生错误，请稍后重试")


# 管理员命令：添加老师
@message_handler(
    filters.TEXT & filters.COMMAND & filters.ChatType.PRIVATE,
    "添加老师命令",
    priority=10
)
async def handle_add_teacher_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理添加老师命令（仅私聊）"""
    message = update.message
    if not message or not message.text:
        return
    
    text = message.text.strip()
    
    # 检查是否是添加老师命令
    if not text.startswith('/add_teacher'):
        return
    
    # 解析命令参数
    # 格式: /add_teacher 昵称 @用户名 频道链接 标签1,标签2,标签3
    parts = text.split(None, 4)  # 最多分割成5部分
    
    if len(parts) < 2:
        await message.reply_text(
            "❌ 命令格式错误\n\n"
            "正确格式：\n"
            "`/add_teacher 昵称 [@用户名] [频道链接] [标签1,标签2,标签3]`\n\n"
            "示例：\n"
            "`/add_teacher 张老师 @zhanglaoshi https://t.me/zhanglaoshi_channel 数学,高中,在线教学`",
            parse_mode='Markdown'
        )
        return
    
    try:
        # 解析参数
        nickname = parts[1]
        username = None
        channel_link = None
        tags = []
        
        # 解析可选参数
        for i in range(2, len(parts)):
            part = parts[i].strip()
            if part.startswith('@'):
                username = part[1:]  # 去掉@符号
            elif part.startswith('http'):
                channel_link = part
            else:
                # 假设是标签
                tags.extend([tag.strip() for tag in part.split(',') if tag.strip()])
        
        # 创建老师对象
        from src.models.teacher import Teacher
        teacher = Teacher(
            nickname=nickname,
            username=username,
            channel_link=channel_link,
            tags=tags
        )
        
        # 添加到管理器
        teacher_id = teacher_manager.add_teacher(teacher)
        
        # 回复成功信息
        await message.reply_text(
            f"✅ 成功添加老师：\n\n"
            f"{teacher.get_full_info()}\n\n"
            f"老师ID: `{teacher_id}`",
            parse_mode='Markdown'
        )
        
        logger.info(f"用户 {message.from_user.id} 添加了老师: {teacher.nickname}")
        
    except Exception as e:
        logger.error(f"添加老师时出错: {e}")
        await message.reply_text(f"❌ 添加老师时发生错误: {str(e)}")


# 管理员命令：查看所有老师
@message_handler(
    filters.TEXT & filters.COMMAND & filters.ChatType.PRIVATE,
    "查看所有老师命令",
    priority=10
)
async def handle_list_teachers_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理查看所有老师命令（仅私聊）"""
    message = update.message
    if not message or not message.text:
        return
    
    text = message.text.strip()
    
    # 检查是否是查看老师命令
    if text != '/list_teachers':
        return
    
    try:
        teachers = teacher_manager.get_all_teachers()
        
        if not teachers:
            await message.reply_text("📝 老师标签库为空")
            return
        
        # 构建老师列表
        result_lines = [f"📚 老师标签库 (共 {len(teachers)} 位老师)：\n"]
        
        for i, teacher in enumerate(teachers, 1):
            teacher_info = f"{i}. **{teacher.nickname}**"
            
            if teacher.username:
                teacher_info += f" (@{teacher.username})"
            
            if teacher.channel_link:
                teacher_info += f"\n   📺 {teacher.channel_link}"
            
            if teacher.tags:
                teacher_info += f"\n   🏷️ {', '.join(teacher.tags)}"
            
            result_lines.append(teacher_info)
        
        result_text = "\n\n".join(result_lines)
        
        # 如果消息太长，分批发送
        if len(result_text) > 4000:
            # 分批发送
            batch_size = 10
            for i in range(0, len(teachers), batch_size):
                batch_teachers = teachers[i:i + batch_size]
                batch_lines = [f"📚 老师标签库 (第 {i//batch_size + 1} 批)：\n"]
                
                for j, teacher in enumerate(batch_teachers, i + 1):
                    teacher_info = f"{j}. **{teacher.nickname}**"
                    
                    if teacher.username:
                        teacher_info += f" (@{teacher.username})"
                    
                    if teacher.channel_link:
                        teacher_info += f"\n   📺 {teacher.channel_link}"
                    
                    if teacher.tags:
                        teacher_info += f"\n   🏷️ {', '.join(teacher.tags)}"
                    
                    batch_lines.append(teacher_info)
                
                batch_text = "\n\n".join(batch_lines)
                await message.reply_text(batch_text, parse_mode='Markdown', disable_web_page_preview=True)
        else:
            await message.reply_text(result_text, parse_mode='Markdown', disable_web_page_preview=True)
        
        logger.info(f"用户 {message.from_user.id} 查看了老师列表")
        
    except Exception as e:
        logger.error(f"查看老师列表时出错: {e}")
        await message.reply_text(f"❌ 查看老师列表时发生错误: {str(e)}")


# 管理员命令：查看所有标签
@message_handler(
    filters.TEXT & filters.COMMAND & filters.ChatType.PRIVATE,
    "查看所有标签命令",
    priority=10
)
async def handle_list_tags_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理查看所有标签命令（仅私聊）"""
    message = update.message
    if not message or not message.text:
        return
    
    text = message.text.strip()
    
    # 检查是否是查看标签命令
    if text != '/list_tags':
        return
    
    try:
        all_tags = teacher_manager.get_all_tags()
        teachers_count = teacher_manager.get_teachers_count()
        
        if not all_tags:
            await message.reply_text("🏷️ 暂无标签数据")
            return
        
        # 构建标签列表
        tags_text = ", ".join(all_tags)
        result_text = (
            f"🏷️ **所有标签** (共 {len(all_tags)} 个)：\n\n"
            f"{tags_text}\n\n"
            f"📊 统计信息：\n"
            f"• 老师总数：{teachers_count}\n"
            f"• 标签总数：{len(all_tags)}"
        )
        
        await message.reply_text(result_text, parse_mode='Markdown')
        
        logger.info(f"用户 {message.from_user.id} 查看了标签列表")
        
    except Exception as e:
        logger.error(f"查看标签列表时出错: {e}")
        await message.reply_text(f"❌ 查看标签列表时发生错误: {str(e)}")
