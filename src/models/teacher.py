"""
老师数据模型

定义老师的数据结构和相关操作
"""

from typing import List, Dict, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import json


@dataclass
class Teacher:
    """老师数据模型"""
    
    nickname: str  # 昵称
    username: Optional[str] = None  # 用户名（@username）
    channel_link: Optional[str] = None  # 频道链接
    tags: List[str] = None  # 标签列表
    created_at: Optional[str] = None  # 创建时间
    updated_at: Optional[str] = None  # 更新时间
    
    def __post_init__(self):
        """初始化后处理"""
        if self.tags is None:
            self.tags = []
        
        # 设置时间戳
        current_time = datetime.now().isoformat()
        if self.created_at is None:
            self.created_at = current_time
        if self.updated_at is None:
            self.updated_at = current_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Teacher':
        """从字典创建实例"""
        return cls(**data)
    
    def add_tag(self, tag: str) -> bool:
        """添加标签"""
        tag = tag.strip().lower()
        if tag and tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = datetime.now().isoformat()
            return True
        return False
    
    def remove_tag(self, tag: str) -> bool:
        """移除标签"""
        tag = tag.strip().lower()
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.now().isoformat()
            return True
        return False
    
    def has_tag(self, tag: str) -> bool:
        """检查是否有指定标签"""
        return tag.strip().lower() in self.tags
    
    def has_any_tags(self, tags: List[str]) -> bool:
        """检查是否有任意一个指定标签"""
        search_tags = [tag.strip().lower() for tag in tags]
        return any(tag in self.tags for tag in search_tags)
    
    def has_all_tags(self, tags: List[str]) -> bool:
        """检查是否包含所有指定标签"""
        search_tags = [tag.strip().lower() for tag in tags]
        return all(tag in self.tags for tag in search_tags)
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        if self.username:
            return f"@{self.username}"
        return self.nickname
    
    def get_full_info(self) -> str:
        """获取完整信息字符串"""
        info_parts = []
        
        # 昵称
        info_parts.append(f"昵称: {self.nickname}")
        
        # 用户名
        if self.username:
            info_parts.append(f"用户名: @{self.username}")
        
        # 频道链接
        if self.channel_link:
            info_parts.append(f"频道: {self.channel_link}")
        
        # 标签
        if self.tags:
            info_parts.append(f"标签: {', '.join(self.tags)}")
        
        return "\n".join(info_parts)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Teacher(nickname='{self.nickname}', username='{self.username}', tags={self.tags})"
