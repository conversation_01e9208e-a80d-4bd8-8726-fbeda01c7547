"""
抽奖数据库配置管理模块

使用MySQL数据库替代JSON文件存储抽奖活动数据
"""

import json
from mysql.connector import pooling
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from contextlib import contextmanager

from src.core.logger import default_logger as logger
from src.models.lottery import LotteryActivity, LotteryParticipant, LotteryStatus, Prize
from database.mysql_config import get_mysql_pool_config


class LotteryDatabaseConfig:
    """抽奖数据库配置管理器"""
    
    def __init__(self):
        self.connection_pool = None
        self._init_connection_pool()
    
    def _init_connection_pool(self):
        """初始化数据库连接池"""
        try:
            pool_config = get_mysql_pool_config()
            self.connection_pool = pooling.MySQLConnectionPool(**pool_config)
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"初始化数据库连接池失败: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = self.connection_pool.get_connection()
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库操作出错: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def create_activity(self, activity: LotteryActivity) -> bool:
        """创建抽奖活动"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 开始事务
                conn.start_transaction()
                
                try:
                    # 1. 插入主活动记录
                    activity_sql = """
                    INSERT INTO lottery_activities 
                    (id, creator_id, name, password, draw_time, cover_photo, description,
                     prize_name, prize_count, winner_count, status, create_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    activity_values = (
                        activity.id, activity.creator_id, activity.name, activity.password,
                        activity.draw_time, activity.cover_photo, activity.description,
                        activity.prize_name, activity.prize_count, activity.winner_count,
                        activity.status.value, activity.create_time
                    )
                    cursor.execute(activity_sql, activity_values)
                    
                    # 2. 插入目标群组
                    if activity.target_group_ids:
                        group_sql = """
                        INSERT INTO lottery_target_groups (activity_id, group_id, group_name)
                        VALUES (%s, %s, %s)
                        """
                        for i, group_id in enumerate(activity.target_group_ids):
                            group_name = activity.target_group_names[i] if i < len(activity.target_group_names) else None
                            cursor.execute(group_sql, (activity.id, group_id, group_name))
                    
                    # 3. 插入奖品信息
                    if activity.prizes:
                        prize_sql = """
                        INSERT INTO lottery_prizes (activity_id, name, count)
                        VALUES (%s, %s, %s)
                        """
                        for prize in activity.prizes:
                            cursor.execute(prize_sql, (activity.id, prize.name, prize.count))
                    
                    # 4. 插入参与者（如果有）
                    if activity.participants:
                        participant_sql = """
                        INSERT INTO lottery_participants
                        (activity_id, user_id, username, first_name, last_name, join_time, won_prize, is_winner, win_time)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        for participant in activity.participants:
                            is_winner = participant in activity.winners
                            cursor.execute(participant_sql, (
                                activity.id, participant.user_id, participant.username,
                                participant.first_name, participant.last_name, participant.join_time,
                                participant.won_prize, is_winner, participant.win_time
                            ))
                    
                    # 提交事务
                    conn.commit()
                    logger.info(f"创建抽奖活动成功: {activity.id} - {activity.name}")
                    return True
                    
                except Exception as e:
                    conn.rollback()
                    logger.error(f"创建抽奖活动失败: {e}")
                    return False
                finally:
                    cursor.close()
                    
        except Exception as e:
            logger.error(f"创建抽奖活动时数据库连接出错: {e}")
            return False
    
    def get_activity(self, activity_id: str) -> Optional[LotteryActivity]:
        """获取抽奖活动"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                
                # 1. 获取活动基本信息
                activity_sql = "SELECT * FROM lottery_activities WHERE id = %s"
                cursor.execute(activity_sql, (activity_id,))
                activity_data = cursor.fetchone()
                
                if not activity_data:
                    return None
                
                # 2. 获取目标群组
                group_sql = "SELECT group_id, group_name FROM lottery_target_groups WHERE activity_id = %s"
                cursor.execute(group_sql, (activity_id,))
                groups = cursor.fetchall()
                
                target_group_ids = [g['group_id'] for g in groups]
                target_group_names = [g['group_name'] or '' for g in groups]
                
                # 3. 获取奖品信息
                prize_sql = "SELECT name, count FROM lottery_prizes WHERE activity_id = %s"
                cursor.execute(prize_sql, (activity_id,))
                prize_data = cursor.fetchall()
                prizes = [Prize(name=p['name'], count=p['count']) for p in prize_data]
                
                # 4. 获取参与者
                participant_sql = """
                SELECT user_id, username, first_name, last_name, join_time, won_prize, is_winner, win_time
                FROM lottery_participants WHERE activity_id = %s ORDER BY join_time
                """
                cursor.execute(participant_sql, (activity_id,))
                participant_data = cursor.fetchall()
                
                participants = []
                winners = []
                
                for p in participant_data:
                    participant = LotteryParticipant(
                        user_id=p['user_id'],
                        username=p['username'],
                        first_name=p['first_name'],
                        last_name=p['last_name'],
                        join_time=p['join_time'],
                        won_prize=p['won_prize'],
                        win_time=p['win_time']
                    )
                    participants.append(participant)
                    
                    if p['is_winner']:
                        winners.append(participant)
                
                # 构建活动对象
                activity = LotteryActivity(
                    id=activity_data['id'],
                    creator_id=activity_data['creator_id'],
                    name=activity_data['name'],
                    password=activity_data['password'],
                    draw_time=activity_data['draw_time'],
                    target_group_ids=target_group_ids,
                    target_group_names=target_group_names,
                    cover_photo=activity_data['cover_photo'],
                    description=activity_data['description'],
                    prizes=prizes,
                    prize_name=activity_data['prize_name'],
                    prize_count=activity_data['prize_count'],
                    winner_count=activity_data['winner_count'],
                    status=LotteryStatus(activity_data['status']),
                    participants=participants,
                    winners=winners,
                    create_time=activity_data['create_time']
                )
                
                cursor.close()
                return activity
                
        except Exception as e:
            logger.error(f"获取抽奖活动时出错: {e}")
            return None
    
    def update_activity(self, activity: LotteryActivity) -> bool:
        """更新抽奖活动"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 开始事务
                conn.start_transaction()
                
                try:
                    # 1. 更新主活动记录
                    activity_sql = """
                    UPDATE lottery_activities SET
                        name = %s, password = %s, draw_time = %s, cover_photo = %s, description = %s,
                        prize_name = %s, prize_count = %s, winner_count = %s, status = %s,
                        update_time = %s
                    WHERE id = %s
                    """
                    cursor.execute(activity_sql, (
                        activity.name, activity.password, activity.draw_time,
                        activity.cover_photo, activity.description,
                        activity.prize_name, activity.prize_count, activity.winner_count,
                        activity.status.value, datetime.now(), activity.id
                    ))
                    
                    # 2. 删除并重新插入目标群组
                    cursor.execute("DELETE FROM lottery_target_groups WHERE activity_id = %s", (activity.id,))
                    if activity.target_group_ids:
                        group_sql = """
                        INSERT INTO lottery_target_groups (activity_id, group_id, group_name)
                        VALUES (%s, %s, %s)
                        """
                        for i, group_id in enumerate(activity.target_group_ids):
                            group_name = activity.target_group_names[i] if i < len(activity.target_group_names) else None
                            cursor.execute(group_sql, (activity.id, group_id, group_name))
                    
                    # 3. 删除并重新插入奖品
                    cursor.execute("DELETE FROM lottery_prizes WHERE activity_id = %s", (activity.id,))
                    if activity.prizes:
                        prize_sql = """
                        INSERT INTO lottery_prizes (activity_id, name, count)
                        VALUES (%s, %s, %s)
                        """
                        for prize in activity.prizes:
                            cursor.execute(prize_sql, (activity.id, prize.name, prize.count))
                    
                    # 4. 删除并重新插入参与者
                    cursor.execute("DELETE FROM lottery_participants WHERE activity_id = %s", (activity.id,))
                    if activity.participants:
                        participant_sql = """
                        INSERT INTO lottery_participants
                        (activity_id, user_id, username, first_name, last_name, join_time, won_prize, is_winner, win_time)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        for participant in activity.participants:
                            is_winner = participant in activity.winners
                            cursor.execute(participant_sql, (
                                activity.id, participant.user_id, participant.username,
                                participant.first_name, participant.last_name, participant.join_time,
                                participant.won_prize, is_winner, participant.win_time
                            ))
                    
                    # 提交事务
                    conn.commit()
                    logger.info(f"更新抽奖活动成功: {activity.id}")
                    return True
                    
                except Exception as e:
                    conn.rollback()
                    logger.error(f"更新抽奖活动失败: {e}")
                    return False
                finally:
                    cursor.close()
                    
        except Exception as e:
            logger.error(f"更新抽奖活动时数据库连接出错: {e}")
            return False

    def delete_activity(self, activity_id: str) -> bool:
        """删除抽奖活动"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 由于设置了外键级联删除，只需删除主表记录
                sql = "DELETE FROM lottery_activities WHERE id = %s"
                cursor.execute(sql, (activity_id,))
                conn.commit()

                if cursor.rowcount > 0:
                    logger.info(f"删除抽奖活动成功: {activity_id}")
                    cursor.close()
                    return True
                else:
                    logger.warning(f"未找到要删除的抽奖活动: {activity_id}")
                    cursor.close()
                    return False

        except Exception as e:
            logger.error(f"删除抽奖活动时出错: {e}")
            return False

    def get_activities_by_creator(self, creator_id: int) -> List[LotteryActivity]:
        """获取指定创建者的所有抽奖活动"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)

                sql = """
                SELECT id FROM lottery_activities
                WHERE creator_id = %s
                ORDER BY create_time DESC
                """
                cursor.execute(sql, (creator_id,))
                activity_ids = [row['id'] for row in cursor.fetchall()]
                cursor.close()

                # 获取每个活动的完整信息
                activities = []
                for activity_id in activity_ids:
                    activity = self.get_activity(activity_id)
                    if activity:
                        activities.append(activity)

                return activities

        except Exception as e:
            logger.error(f"获取创建者抽奖活动时出错: {e}")
            return []

    def find_activity_by_password(self, password: str, group_id: int) -> Optional[LotteryActivity]:
        """通过口令和群组ID查找活动"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)

                sql = """
                SELECT DISTINCT la.id
                FROM lottery_activities la
                JOIN lottery_target_groups ltg ON la.id = ltg.activity_id
                WHERE la.password = %s AND ltg.group_id = %s AND la.status = 'waiting'
                """
                cursor.execute(sql, (password, group_id))
                result = cursor.fetchone()
                cursor.close()

                if result:
                    return self.get_activity(result['id'])
                return None

        except Exception as e:
            logger.error(f"通过口令查找活动时出错: {e}")
            return None

    def get_ready_to_draw_activities(self) -> List[LotteryActivity]:
        """获取准备开奖的活动"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)

                sql = """
                SELECT id FROM lottery_activities
                WHERE status = 'waiting' AND draw_time <= NOW()
                ORDER BY draw_time
                """
                cursor.execute(sql)
                activity_ids = [row['id'] for row in cursor.fetchall()]
                cursor.close()

                # 获取每个活动的完整信息
                activities = []
                for activity_id in activity_ids:
                    activity = self.get_activity(activity_id)
                    if activity:
                        activities.append(activity)

                return activities

        except Exception as e:
            logger.error(f"获取准备开奖活动时出错: {e}")
            return []

    def get_user_creation_state(self, user_id: int) -> Optional[Dict]:
        """获取用户创建状态"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)

                sql = "SELECT state_data FROM lottery_user_states WHERE user_id = %s"
                cursor.execute(sql, (user_id,))
                result = cursor.fetchone()
                cursor.close()

                if result and result['state_data']:
                    return json.loads(result['state_data'])
                return None

        except Exception as e:
            logger.error(f"获取用户创建状态时出错: {e}")
            return None

    def set_user_creation_state(self, user_id: int, state_data: Dict) -> bool:
        """设置用户创建状态"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                state_json = json.dumps(state_data, ensure_ascii=False)

                # 使用 ON DUPLICATE KEY UPDATE 语法
                sql = """
                INSERT INTO lottery_user_states (user_id, state_data, create_time, update_time)
                VALUES (%s, %s, NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                    state_data = VALUES(state_data),
                    update_time = NOW()
                """
                cursor.execute(sql, (user_id, state_json))
                conn.commit()
                cursor.close()

                logger.debug(f"设置用户创建状态成功: {user_id}")
                return True

        except Exception as e:
            logger.error(f"设置用户创建状态时出错: {e}")
            return False

    def delete_user_creation_state(self, user_id: int) -> bool:
        """删除用户创建状态"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = "DELETE FROM lottery_user_states WHERE user_id = %s"
                cursor.execute(sql, (user_id,))
                conn.commit()

                success = cursor.rowcount > 0
                cursor.close()

                if success:
                    logger.debug(f"删除用户创建状态成功: {user_id}")

                return success

        except Exception as e:
            logger.error(f"删除用户创建状态时出错: {e}")
            return False

    def get_all_user_creation_states(self) -> Dict[str, Dict]:
        """获取所有用户创建状态"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)

                sql = "SELECT user_id, state_data FROM lottery_user_states"
                cursor.execute(sql)
                results = cursor.fetchall()
                cursor.close()

                states = {}
                for row in results:
                    user_id = str(row['user_id'])
                    if row['state_data']:
                        states[user_id] = json.loads(row['state_data'])

                return states

        except Exception as e:
            logger.error(f"获取所有用户创建状态时出错: {e}")
            return {}

    def cleanup_expired_user_states(self, days_to_keep: int = 7) -> int:
        """清理过期的用户状态数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 调用存储过程
                cursor.callproc('CleanExpiredUserStates', [days_to_keep])

                # 获取结果
                for result in cursor.stored_results():
                    row = result.fetchone()
                    if row:
                        deleted_count = row[0]
                        logger.info(f"清理过期用户状态数据: {deleted_count} 条")
                        cursor.close()
                        return deleted_count

                cursor.close()
                return 0

        except Exception as e:
            logger.error(f"清理过期用户状态数据时出错: {e}")
            return 0
