"""
抽奖配置管理模块

管理抽奖活动数据的存储和读取 - MySQL数据库版本
"""

from typing import List, Dict, Optional
from datetime import datetime
from src.core.logger import default_logger as logger
from src.models.lottery import LotteryActivity, LotteryStatus
from src.config.lottery_db_config import LotteryDatabaseConfig


class LotteryConfig:
    """抽奖配置管理器 - 数据库版本"""

    def __init__(self):
        # 使用数据库配置替代JSON文件
        self.db_config = LotteryDatabaseConfig()
        logger.info("抽奖配置管理器初始化完成 - 使用MySQL数据库")

    def create_activity(self, activity: LotteryActivity) -> bool:
        """创建抽奖活动"""
        return self.db_config.create_activity(activity)

    def get_activity(self, activity_id: str) -> Optional[LotteryActivity]:
        """获取抽奖活动"""
        return self.db_config.get_activity(activity_id)

    def update_activity(self, activity: LotteryActivity) -> bool:
        """更新抽奖活动"""
        return self.db_config.update_activity(activity)
    
    def delete_activity(self, activity_id: str) -> bool:
        """删除抽奖活动"""
        return self.db_config.delete_activity(activity_id)

    def get_activities_by_creator(self, creator_id: int) -> List[LotteryActivity]:
        """获取用户创建的抽奖活动"""
        return self.db_config.get_activities_by_creator(creator_id)

    def find_activity_by_password(self, password: str, group_id: int) -> Optional[LotteryActivity]:
        """通过口令和群组ID查找活动"""
        return self.db_config.find_activity_by_password(password, group_id)

    def get_ready_to_draw_activities(self) -> List[LotteryActivity]:
        """获取准备开奖的活动"""
        return self.db_config.get_ready_to_draw_activities()

    # 用户创建状态管理
    def set_user_creation_state(self, user_id: int, state: Dict):
        """设置用户创建状态"""
        self.db_config.set_user_creation_state(user_id, state)

    def get_user_creation_state(self, user_id: int) -> Optional[Dict]:
        """获取用户创建状态"""
        return self.db_config.get_user_creation_state(user_id)

    def clear_user_creation_state(self, user_id: int):
        """清除用户创建状态"""
        self.db_config.delete_user_creation_state(user_id)

    def delete_user_creation_state(self, user_id: int):
        """删除用户创建状态"""
        self.db_config.delete_user_creation_state(user_id)


# 全局配置实例
lottery_config = LotteryConfig()
