# 老师标签库功能说明

## 功能概述

老师标签库功能允许用户在群组中通过标签搜索老师信息。系统会根据用户提供的标签匹配相应的老师，并返回老师的用户名、频道链接等信息。

## 数据结构

每个老师包含以下信息：
- **昵称** (nickname): 老师的显示名称
- **用户名** (username): Telegram用户名（可选）
- **频道链接** (channel_link): 老师的频道链接（可选）
- **标签** (tags): 标签列表，用于搜索匹配

## 使用方法

### 群组搜索功能

在群组中发送以下格式的消息来搜索老师：

```
search_tag 标签1,标签2,标签3
```

**示例：**
- `search_tag 数学` - 搜索数学老师
- `search_tag 高中,物理` - 搜索高中物理老师
- `search_tag 英语,口语,在线教学` - 搜索英语口语在线教学老师

**搜索规则：**
- 不区分大小写
- 多个标签用逗号分隔
- 匹配任意一个标签即可返回结果
- 如果没有匹配结果，返回"暂无匹配结果"

### 管理功能（私聊机器人）

#### 添加老师
```
/add_teacher 昵称 [@用户名] [频道链接] [标签1,标签2,标签3]
```

**示例：**
```
/add_teacher 张老师 @zhanglaoshi https://t.me/zhangchannel 数学,高中,奥数
/add_teacher 李老师 @liteacher 英语,口语
/add_teacher 王老师 物理,实验
```

#### 查看所有老师
```
/list_teachers
```

#### 查看所有标签
```
/list_tags
```

## 搜索结果格式

当找到匹配的老师时，机器人会返回以下格式的信息：

```
🔍 找到 2 位老师：

1. **张老师** (@zhanglaoshi)
   📺 频道: https://t.me/zhanglaoshi_channel
   🏷️ 标签: 数学, 高中, 在线教学, 奥数

2. **李老师** (@liteacher)
   📺 频道: https://t.me/liteacher_english
   🏷️ 标签: 英语, 初中, 口语, 语法
```

## 技术实现

### 文件结构
```
src/
├── models/
│   └── teacher.py          # 老师数据模型
├── core/
│   └── teacher_manager.py  # 老师标签管理器
└── handlers/
    └── teacher_handler.py  # 搜索处理器

data/
└── teachers.json          # 老师数据存储
```

### 核心组件

1. **Teacher 模型** (`src/models/teacher.py`)
   - 定义老师的数据结构
   - 提供标签操作方法
   - 支持数据序列化和反序列化

2. **TeacherManager 管理器** (`src/core/teacher_manager.py`)
   - 管理老师数据的增删改查
   - 实现标签搜索算法
   - 处理数据持久化

3. **搜索处理器** (`src/handlers/teacher_handler.py`)
   - 处理群组中的搜索命令
   - 解析搜索参数
   - 格式化搜索结果
   - 提供管理命令

## 示例数据

系统预置了以下示例老师数据：

1. **张老师** (@zhanglaoshi)
   - 频道: https://t.me/zhanglaoshi_channel
   - 标签: 数学, 高中, 在线教学, 奥数

2. **李老师** (@liteacher)
   - 频道: https://t.me/liteacher_english
   - 标签: 英语, 初中, 口语, 语法

3. **王老师**
   - 频道: https://t.me/wangteacher_physics
   - 标签: 物理, 高中, 实验, 竞赛

4. **陈老师** (@chenteacher)
   - 标签: 化学, 初中, 高中, 实验

5. **刘老师** (@liuteacher)
   - 频道: https://t.me/liuteacher_history
   - 标签: 历史, 高中, 文科, 古代史

## 注意事项

1. **优先级设置**: 标签搜索处理器的优先级设置为5，低于抽奖口令处理器（优先级10），确保不会干扰现有的抽奖功能。

2. **数据安全**: 老师数据存储在 `data/teachers.json` 文件中，建议定期备份。

3. **权限控制**: 管理命令（添加老师、查看列表等）只能在私聊中使用，确保数据安全。

4. **性能考虑**: 当老师数量较多时，列表显示会自动分批发送，避免消息过长。

## 扩展功能

未来可以考虑添加的功能：
- 老师信息编辑和删除
- 标签分类管理
- 搜索结果排序
- 老师评分系统
- 数据导入导出功能
