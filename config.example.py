"""
配置文件示例

复制此文件为 config.py 并修改相应的配置项
"""

import os
from pydantic import BaseModel, HttpUrl
from typing import List


class Settings(BaseModel):
    # 基础配置
    HOST: str = "localhost"
    PORT: int = 8999
    WORKERS: int = 1
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"

    # Telegram Bot配置
    # 从 @BotFather 获取的 Bot Token
    TELEGRAM_BOT_TOKEN: str = "YOUR_BOT_TOKEN_HERE"

    # Webhook 配置（如果使用 Webhook 模式）
    # 例如使用 ngrok: https://xxxxx.ngrok-free.app
    WEBHOOK_BASE_URL: HttpUrl = 'YOUR_WEBHOOK_URL_HERE'
    WEBHOOK_PATH: str = "/webhook"

    # 数据库配置
    # MySQL数据库连接配置
    # 这些配置项会优先使用环境变量，如果环境变量不存在则使用下面的默认值
    MYSQL_HOST: str = os.getenv('MYSQL_HOST', 'localhost')
    MYSQL_PORT: int = int(os.getenv('MYSQL_PORT', '3306'))
    MYSQL_USER: str = os.getenv('MYSQL_USER', 'root')
    MYSQL_PASSWORD: str = os.getenv('MYSQL_PASSWORD', 'your_password_here')  # 请修改为实际密码
    MYSQL_DATABASE: str = os.getenv('MYSQL_DATABASE', 'tg_syl_data')
    MYSQL_CHARSET: str = 'utf8mb4'
    MYSQL_AUTOCOMMIT: bool = False
    MYSQL_CONNECT_TIMEOUT: int = 10
    MYSQL_SQL_MODE: str = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
    MYSQL_TIME_ZONE: str = '+08:00'

    # MySQL连接池配置
    MYSQL_POOL_NAME: str = 'lottery_pool'
    MYSQL_POOL_SIZE: int = 10
    MYSQL_POOL_RESET_SESSION: bool = True
    MYSQL_BUFFERED: bool = True

    # 管理员配置（可选）
    # 全局管理员ID列表，这些用户在任何群组都有管理权限
    # 通常情况下，机器人会自动检查群组管理员权限，无需设置此项
    # 只有在需要跨群组管理或私聊管理时才需要设置
    ADMIN_USER_IDS: List[int] = [
        # 123456789,  # 替换为你的用户ID（可选）
        # 987654321,  # 可以添加多个全局管理员（可选）
    ]

    # 新成员验证配置
    MEMBER_VERIFICATION_ENABLED: bool = True  # 是否启用新成员验证
    VERIFICATION_TIMEOUT_MINUTES: int = 5  # 验证超时时间（分钟）
    VERIFICATION_QUESTION_DIFFICULTY: str = "easy"  # 题目难度: easy, medium, hard


# 创建单例实例
settings = Settings()


# 配置说明：
#
# 1. TELEGRAM_BOT_TOKEN:
#    - 在 Telegram 中找到 @BotFather
#    - 发送 /newbot 创建机器人
#    - 获取 Token 并替换 YOUR_BOT_TOKEN_HERE
#
# 2. WEBHOOK_BASE_URL:
#    - 如果使用 Webhook 模式，需要公网可访问的 URL
#    - 推荐使用 ngrok: ngrok http 8999
#    - 将生成的 https URL 替换 YOUR_WEBHOOK_URL_HERE
#
# 3. 数据库配置:
#    - MYSQL_HOST: MySQL服务器地址
#    - MYSQL_PORT: MySQL服务器端口（默认3306）
#    - MYSQL_USER: 数据库用户名
#    - MYSQL_PASSWORD: 数据库密码（请修改为实际密码）
#    - MYSQL_DATABASE: 数据库名称
#    - 其他MySQL配置项通常使用默认值即可
#
# 4. ADMIN_USER_IDS（可选）:
#    - 机器人会自动检查群组管理员权限
#    - 只有需要全局管理权限时才需要设置
#    - 在 Telegram 中找到 @userinfobot 获取用户ID
#
# 5. 新成员验证:
#    - MEMBER_VERIFICATION_ENABLED: 是否启用新成员验证功能
#    - VERIFICATION_TIMEOUT_MINUTES: 新成员验证的超时时间
#    - VERIFICATION_QUESTION_DIFFICULTY: 验证题目的难度级别
#
# 6. 环境变量优先级:
#    - 所有配置项都支持通过环境变量设置
#    - 环境变量的优先级高于配置文件中的默认值
#    - 例如：export MYSQL_PASSWORD=your_secure_password
