#!/usr/bin/env python3
"""
Telegram 广告删除机器人启动脚本

这个脚本提供了一个简单的启动界面，帮助用户配置和启动机器人
"""

import os
import sys
import asyncio
from pathlib import Path

def check_config():
    """检查配置文件是否正确设置"""
    try:
        from config import settings
        
        print("🔍 检查配置...")
        
        # 检查必要的配置项
        if not settings.TELEGRAM_BOT_TOKEN or settings.TELEGRAM_BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            print("❌ 错误: 请在 config.py 中设置正确的 TELEGRAM_BOT_TOKEN")
            return False
        
        if not settings.ADMIN_USER_IDS:
            print("ℹ️  提示: 未设置全局管理员ID")
            print("   机器人会自动检查群组管理员权限，群组管理员可以使用管理功能")
            print("   如需跨群组管理权限，可在 config.py 中设置 ADMIN_USER_IDS")
        
        print("✅ 配置检查完成")
        return True
        
    except ImportError as e:
        print(f"❌ 错误: 无法导入配置文件: {e}")
        return False
    except Exception as e:
        print(f"❌ 错误: 配置检查失败: {e}")
        return False

def check_dependencies():
    """检查依赖包是否安装"""
    print("📦 检查依赖包...")
    
    required_packages = [
        'telegram',
        'fastapi',
        'uvicorn',
        'pydantic',
        'httpx'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 依赖包检查完成")
    return True

def create_data_directory():
    """创建数据目录"""
    data_dir = Path("data")
    if not data_dir.exists():
        data_dir.mkdir()
        print("📁 创建数据目录: data/")

def show_startup_info():
    """显示启动信息"""
    print("=" * 60)
    print("🤖 Telegram 抽奖机器人")
    print("=" * 60)
    print()

def show_usage_info():
    """显示使用说明"""
    print("\n📋 使用说明:")
    print("1. 将机器人添加到目标群组")
    print("2. 给机器人管理员权限（可选）")
    print("3. 私聊机器人使用 /create 命令创建抽奖")
    print("4. 在群组中发送抽奖口令参与抽奖")
    print()
    print("🎉 抽奖功能命令:")
    print("• /create - 创建抽奖活动（私聊）")
    print("• /my_lotteries - 查看我的抽奖（私聊）")
    print("• /stop_lottery - 取消抽奖活动（私聊）")
    print()
    print("💡 权限说明:")
    print("• 群组管理员自动拥有管理权限")
    print("• 无需在配置文件中设置管理员ID")
    print("• 机器人会自动检查群组管理员身份")
    print()

async def start_bot():
    """启动机器人"""
    try:
        print("🚀 启动机器人...")
        from main import run_server
        await run_server()
    except KeyboardInterrupt:
        print("\n👋 机器人已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    show_startup_info()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查配置
    if not check_config():
        sys.exit(1)
    
    # 创建必要的目录
    create_data_directory()
    
    # 显示使用说明
    show_usage_info()
    
    # 启动机器人
    try:
        asyncio.run(start_bot())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")

if __name__ == "__main__":
    main()
