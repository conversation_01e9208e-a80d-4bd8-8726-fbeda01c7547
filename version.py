"""
版本信息
"""

__version__ = "1.3.0"
__author__ = "TgAdBot Team"
__description__ = "Telegram 抽奖机器人 - 支持多群组抽奖和新成员验证"
__license__ = "MIT"

# 版本历史
VERSION_HISTORY = [
    {
        "version": "1.3.0",
        "date": "2025-08-05",
        "changes": [
            "🧹 代码库清理：删除冗余的缓存文件和未使用的代码",
            "🔧 修复重复的方法定义问题",
            "📝 更新版本信息和项目描述",
            "🗂️ 整理项目结构，提高代码可维护性",
            "📚 完善文档和注释"
        ]
    },
    {
        "version": "1.2.0",
        "date": "2025-07-07",
        "changes": [
            "🔐 新增新成员验证系统：防止垃圾用户的强大验证机制",
            "🧮 智能数学验证题：随机生成加减乘除算术题",
            "⏰ 定时清理机制：5分钟验证超时和自动清理",
            "🛠️ 管理员工具：/unlock 和 /pending 命令",
            "⚙️ 配置支持：支持开关控制和参数调整",
            "🐛 修复 API 兼容性：解决 forward_from_chat 弃用警告",
            "🔧 更新到最新 API：使用 forward_origin 和 MessageOriginChannel",
            "🧪 新增测试工具：兼容性检查和功能测试脚本",
            "📚 完善文档：详细的使用指南和故障排除"
        ]
    },
    {
        "version": "1.1.0",
        "date": "2025-07-03",
        "changes": [
            "🎉 智能权限管理：自动检查群组管理员权限",
            "✨ 新增白名单管理命令",
            "✨ 新增正则表达式管理命令",
            "🔧 优化权限检查逻辑",
            "🔧 改进错误处理和用户体验",
            "📚 更新文档和配置说明",
            "💡 支持多群组独立管理",
            "💡 实时权限验证机制"
        ]
    },
    {
        "version": "1.0.0",
        "date": "2025-07-03",
        "changes": [
            "🎉 初始版本发布",
            "✨ 基础广告检测功能",
            "✨ 关键词匹配和正则表达式支持",
            "✨ 智能模式检测（微信号、QQ号、手机号等）",
            "✨ 白名单功能",
            "✨ 管理员命令和图形化面板",
            "✨ 统计功能",
            "✨ 垃圾信息检测",
            "📚 完整的部署文档"
        ]
    }
]

def get_version_info():
    """获取版本信息"""
    return {
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "license": __license__
    }

def print_version():
    """打印版本信息"""
    print(f"{__description__} v{__version__}")
    print(f"作者: {__author__}")
    print(f"许可证: {__license__}")

def print_changelog():
    """打印更新日志"""
    print(f"{__description__} - 更新历史")
    print("=" * 50)
    
    for version_info in VERSION_HISTORY:
        print(f"\n📦 版本 {version_info['version']} ({version_info['date']})")
        for change in version_info['changes']:
            print(f"  • {change}")

if __name__ == "__main__":
    print_version()
    print()
    print_changelog()
